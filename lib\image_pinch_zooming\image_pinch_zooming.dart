// image_pinch_zooming.dart

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A highly optimized widget for pinch-to-zoom interaction on images.
class ImagePinchZooming extends StatefulWidget {
  const ImagePinchZooming({
    super.key,
    required this.image,
    this.zoomedBackgroundColor = Colors.black54,
    this.hideStatusBarWhileZooming = false,
    this.minScale = 1.0,
    this.maxScale = 4.0,
    this.animationDuration = const Duration(milliseconds: 200),
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.onTwoFingersOn,
    this.onTwoFingersOff,
    this.onZoomStart,
    this.onZoomEnd,
  });

  final Widget image;
  final Color zoomedBackgroundColor;
  final bool hideStatusBarWhileZooming;
  final double minScale;
  final double maxScale;
  final Duration animationDuration;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onTwoFingersOn;
  final VoidCallback? onTwoFingersOff;
  final VoidCallback? onZoomStart;
  final VoidCallback? onZoomEnd;

  @override
  State<ImagePinchZooming> createState() => _ImagePinchZoomingState();
}

class _ImagePinchZoomingState extends State<ImagePinchZooming>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  OverlayEntry? _overlayEntry;
  Offset? _initialFocalPoint;
  Offset? _widgetOrigin;
  Size? _widgetSize;
  bool _isZooming = false;
  bool _isReversing = false;
  int _activePointers = 0;

  // Store original system UI mode for proper restoration
  SystemUiMode? _originalSystemUiMode;

  // Use a single key for overlay management
  final GlobalKey<_PinchZoomOverlayState> _overlayKey = GlobalKey();

  // Track app lifecycle for system UI restoration
  bool _isAppInForeground = true;

  // Keep references to gesture recognizers for proper cleanup
  _CustomScaleGestureRecognizer? _scaleRecognizer;
  _CustomTapGestureRecognizer? _tapRecognizer;

  @override
  void initState() {
    super.initState();

    // Add app lifecycle observer
    WidgetsBinding.instance.addObserver(this);

    // Store original system UI mode if we need to hide status bar
    if (widget.hideStatusBarWhileZooming) {
      _storeOriginalSystemUiMode();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _cleanupOverlay();
    _restoreSystemUI();

    // Properly dispose gesture recognizers
    _scaleRecognizer?.dispose();
    _tapRecognizer?.dispose();

    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        _isAppInForeground = false;
        // Restore system UI when app goes to background
        if (_isZooming && widget.hideStatusBarWhileZooming) {
          _restoreSystemUI();
        }
        break;
      case AppLifecycleState.resumed:
        _isAppInForeground = true;
        // Re-hide system UI when app comes back to foreground
        if (_isZooming && widget.hideStatusBarWhileZooming) {
          _handleSystemUI();
        }
        break;
      case AppLifecycleState.detached:
        _isAppInForeground = false;
        break;
      case AppLifecycleState.hidden:
        _isAppInForeground = false;
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: RawGestureDetector(
        gestures: {
          _CustomScaleGestureRecognizer:
              GestureRecognizerFactoryWithHandlers<
                _CustomScaleGestureRecognizer
              >(
                () {
                  _scaleRecognizer?.dispose();
                  _scaleRecognizer = _CustomScaleGestureRecognizer();
                  return _scaleRecognizer!;
                },
                (_CustomScaleGestureRecognizer instance) {
                  instance.onStart = _handleScaleStart;
                  instance.onUpdate = _handleScaleUpdate;
                  instance.onEnd = _handleScaleEnd;
                },
              ),
          _CustomTapGestureRecognizer:
              GestureRecognizerFactoryWithHandlers<_CustomTapGestureRecognizer>(
                () {
                  _tapRecognizer?.dispose();
                  _tapRecognizer = _CustomTapGestureRecognizer();
                  return _tapRecognizer!;
                },
                (_CustomTapGestureRecognizer instance) {
                  instance.onTap = widget.onTap;
                  instance.onDoubleTap = widget.onDoubleTap;
                  instance.onLongPress = widget.onLongPress;
                },
              ),
        },
        child: Listener(
          onPointerDown: _handlePointerDown,
          onPointerUp: _handlePointerUp,
          onPointerCancel: _handlePointerCancel,
          child: Visibility(
            visible: !_isZooming,
            maintainSize: true,
            maintainAnimation: true,
            maintainState: true,
            child: widget.image,
          ),
        ),
      ),
    );
  }

  void _handlePointerDown(PointerDownEvent event) {
    _activePointers++;

    if (_activePointers >= 2) {
      // Notify tap recognizer about multi-pointer mode
      _tapRecognizer?.setMultiPointerMode(true);
      widget.onTwoFingersOn?.call();
    }
  }

  void _handlePointerUp(PointerUpEvent event) {
    _activePointers = (_activePointers - 1).clamp(0, 10);

    if (_activePointers < 2) {
      // Exit multi-pointer mode
      _tapRecognizer?.setMultiPointerMode(false);
      widget.onTwoFingersOff?.call();

      // Fix: Trigger scale end when fingers are released during zooming
      if (_isZooming && !_isReversing) {
        _handleScaleEndFromPointer();
      }
    }
  }

  void _handlePointerCancel(PointerCancelEvent event) {
    _activePointers = 0;
    // Exit multi-pointer mode
    _tapRecognizer?.setMultiPointerMode(false);
    widget.onTwoFingersOff?.call();

    // Fix: Trigger scale end when gesture is cancelled during zooming
    if (_isZooming && !_isReversing) {
      _handleScaleEndFromPointer();
    }
  }

  void _handleScaleStart(ScaleStartDetails details) {
    if (_overlayEntry != null || _isReversing || _activePointers < 2) return;

    try {
      // Ensure tap recognizer is in multi-pointer mode
      _tapRecognizer?.setMultiPointerMode(true);

      // Safely get widget bounds with null safety
      final renderBox = context.findRenderObject() as RenderBox?;
      if (renderBox?.hasSize != true || !renderBox!.attached) return;

      _widgetSize = renderBox.size;
      _widgetOrigin = renderBox.localToGlobal(Offset.zero);
      _initialFocalPoint = details.focalPoint;

      if (mounted) {
        setState(() {
          _isZooming = true;
        });
      }

      _handleSystemUI();
      widget.onZoomStart?.call();
      _showOverlay();
    } catch (e) {
      // Handle any coordinate calculation errors gracefully
      debugPrint('Error in _handleScaleStart: $e');
      return;
    }
  }

  void _handleScaleUpdate(ScaleUpdateDetails details) {
    if (_isReversing || _activePointers < 2) return;

    // Direct update for smooth real-time performance
    _performScaleUpdate(details);
  }

  void _performScaleUpdate(ScaleUpdateDetails details) {
    // Check if overlay and its state are available
    final overlayState = _overlayKey.currentState;
    if (overlayState == null ||
        _widgetOrigin == null ||
        _initialFocalPoint == null ||
        _widgetSize == null) {
      return;
    }

    try {
      final clampedScale = details.scale.clamp(
        widget.minScale,
        widget.maxScale,
      );

      // Calculate the center of the widget
      final widgetCenter =
          _widgetOrigin! +
          Offset(_widgetSize!.width / 2, _widgetSize!.height / 2);

      // Calculate the focal point relative to the widget center
      final focalPointOffset = details.focalPoint - widgetCenter;
      final initialFocalPointOffset = _initialFocalPoint! - widgetCenter;

      // Calculate the new position considering the scale and focal point
      // Fix: Use correct scaling calculation for focal point positioning
      final scaledOffset = focalPointOffset * (clampedScale - 1.0);
      final initialScaledOffset =
          initialFocalPointOffset * (1.0 - 1.0); // Initial scale is 1.0

      // Fix: Correct translation calculation to maintain proper drag direction
      final translation =
          (details.focalPoint - _initialFocalPoint!) -
          scaledOffset +
          initialScaledOffset;

      final newPosition = _widgetOrigin! + translation;

      overlayState._updateTransform(newPosition, clampedScale);
    } catch (e) {
      debugPrint('Error in _performScaleUpdate: $e');
    }
  }

  void _handleScaleEnd(ScaleEndDetails details) async {
    if (_isReversing || !_isZooming) return;
    await _performScaleEnd();
  }

  // Fix: Add method to handle scale end from pointer events
  void _handleScaleEndFromPointer() async {
    if (_isReversing || !_isZooming) return;
    await _performScaleEnd();
  }

  // Fix: Extract common scale end logic to avoid duplication
  Future<void> _performScaleEnd() async {
    _isReversing = true;
    widget.onZoomEnd?.call();

    try {
      await _overlayKey.currentState?._animateToOriginal();
    } catch (e) {
      debugPrint('Error in scale end animation: $e');
    }

    _cleanupOverlay();
    _restoreSystemUI();

    // Reset gesture recognizer state
    if (_activePointers < 2) {
      _tapRecognizer?.setMultiPointerMode(false);
    }

    if (mounted) {
      setState(() {
        _isZooming = false;
        _isReversing = false;
      });
    }
  }

  void _showOverlay() {
    if (_overlayEntry != null || _widgetSize == null || _widgetOrigin == null) {
      return;
    }

    try {
      final overlay = Overlay.of(context);
      _overlayEntry = OverlayEntry(
        builder: (context) => _PinchZoomOverlay(
          key: _overlayKey,
          image: widget.image,
          originalSize: _widgetSize!,
          originalPosition: _widgetOrigin!,
          backgroundColor: widget.zoomedBackgroundColor,
          animationDuration: widget.animationDuration,
        ),
      );

      overlay.insert(_overlayEntry!);
    } catch (e) {
      debugPrint('Error showing overlay: $e');
      _overlayEntry = null;
    }
  }

  void _cleanupOverlay() {
    try {
      _overlayEntry?.remove();
    } catch (e) {
      debugPrint('Error removing overlay: $e');
    }

    _overlayEntry = null;
    _widgetOrigin = null;
    _widgetSize = null;
    _initialFocalPoint = null;
  }

  void _storeOriginalSystemUiMode() {
    // Note: There's no direct way to query current SystemUiMode in Flutter
    // We'll assume the most common default mode
    _originalSystemUiMode = SystemUiMode.edgeToEdge;
  }

  void _handleSystemUI() {
    if (!widget.hideStatusBarWhileZooming || !_isAppInForeground) return;

    try {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
    } catch (e) {
      debugPrint('Error setting system UI mode: $e');
    }
  }

  void _restoreSystemUI() {
    if (!widget.hideStatusBarWhileZooming) return;

    try {
      // Restore to original mode if stored, otherwise use sensible default
      SystemChrome.setEnabledSystemUIMode(
        _originalSystemUiMode ?? SystemUiMode.edgeToEdge,
      );
    } catch (e) {
      debugPrint('Error restoring system UI mode: $e');
    }
  }
}

// Custom gesture recognizers to handle conflicts better
class _CustomScaleGestureRecognizer extends ScaleGestureRecognizer {
  @override
  void rejectGesture(int pointer) {
    // Only accept gesture if we have multiple pointers (pinch gesture)
    if (pointerCount >= 2) {
      acceptGesture(pointer);
    } else {
      super.rejectGesture(pointer);
    }
  }

  @override
  void didStopTrackingLastPointer(int pointer) {
    // Fix: Ensure scale end is called when last pointer is lifted
    super.didStopTrackingLastPointer(pointer);
  }
}

class _CustomTapGestureRecognizer extends TapGestureRecognizer {
  VoidCallback? onDoubleTap;
  VoidCallback? onLongPress;

  // Track if we're in a multi-pointer scenario
  bool _isMultiPointer = false;

  @override
  void addAllowedPointer(PointerDownEvent event) {
    // Don't handle tap gestures during multi-pointer interactions
    if (_isMultiPointer) {
      return;
    }
    super.addAllowedPointer(event);
  }

  @override
  void rejectGesture(int pointer) {
    // Don't force accept gestures - let the framework handle conflicts properly
    super.rejectGesture(pointer);
  }

  @override
  void dispose() {
    // Ensure clean disposal
    _isMultiPointer = false;
    super.dispose();
  }

  void setMultiPointerMode(bool isMultiPointer) {
    if (_isMultiPointer == isMultiPointer) return;

    _isMultiPointer = isMultiPointer;
    if (isMultiPointer) {
      // Stop tracking all pointers when entering multi-pointer mode
      final currentPointer = primaryPointer;
      if (currentPointer != null) {
        try {
          stopTrackingPointer(currentPointer);
        } catch (e) {
          // Ignore errors during pointer cleanup
          debugPrint('Error stopping pointer tracking: $e');
        }
      }
    }
  }
}

// Overlay widget for zoom effect
class _PinchZoomOverlay extends StatefulWidget {
  final Widget image;
  final Size originalSize;
  final Offset originalPosition;
  final Color backgroundColor;
  final Duration animationDuration;

  const _PinchZoomOverlay({
    super.key,
    required this.image,
    required this.originalSize,
    required this.originalPosition,
    required this.backgroundColor,
    required this.animationDuration,
  });

  @override
  State<_PinchZoomOverlay> createState() => _PinchZoomOverlayState();
}

class _PinchZoomOverlayState extends State<_PinchZoomOverlay>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _positionAnimation;
  late Animation<double> _opacityAnimation;

  // Use ValueNotifiers for better performance
  late ValueNotifier<Offset> _positionNotifier;
  late ValueNotifier<double> _scaleNotifier;
  bool _isAnimating = false;

  // Cache screen size and opacity calculations for performance
  Size? _cachedScreenSize;
  double _cachedMaxScale = 1.0;

  @override
  void initState() {
    super.initState();
    _positionNotifier = ValueNotifier<Offset>(widget.originalPosition);
    _scaleNotifier = ValueNotifier<double>(1.0);
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
  }

  @override
  void dispose() {
    if (_animationController.isAnimating) {
      _animationController.stop();
    }
    _animationController.dispose();
    _positionNotifier.dispose();
    _scaleNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Cache screen size and max scale on first build
    if (_cachedScreenSize == null) {
      _cachedScreenSize = MediaQuery.of(context).size;
      _cachedMaxScale = _cachedScreenSize!.height / widget.originalSize.height;
    }

    if (_isAnimating) {
      return AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return _buildOverlayContent(
            _positionAnimation.value,
            _scaleAnimation.value,
            _opacityAnimation.value,
          );
        },
      );
    }

    return ValueListenableBuilder<Offset>(
      valueListenable: _positionNotifier,
      builder: (context, position, child) {
        return ValueListenableBuilder<double>(
          valueListenable: _scaleNotifier,
          builder: (context, scale, child) {
            return _buildOverlayContent(position, scale, _getOpacity(scale));
          },
        );
      },
    );
  }

  Widget _buildOverlayContent(Offset position, double scale, double opacity) {
    return RepaintBoundary(
      child: Stack(
        children: [
          // Background with direct opacity for better performance
          if (opacity > 0.0)
            Container(
              width: double.infinity,
              height: double.infinity,
              color: widget.backgroundColor.withValues(alpha: opacity),
            ),
          // Zoomed image with optimized single transform
          RepaintBoundary(
            child: Transform(
              transform: Matrix4.identity()
                ..translate(position.dx, position.dy)
                ..translate(
                  widget.originalSize.width / 2,
                  widget.originalSize.height / 2,
                )
                ..scale(scale)
                ..translate(
                  -widget.originalSize.width / 2,
                  -widget.originalSize.height / 2,
                ),
              child: SizedBox(
                width: widget.originalSize.width,
                height: widget.originalSize.height,
                child: widget.image,
              ),
            ),
          ),
        ],
      ),
    );
  }

  double _getOpacity(double scale) {
    // Optimized opacity calculation with cached values
    if (scale <= 1.0) return 0.0;
    if (_cachedMaxScale <= 1.0) return 1.0;

    return ((scale - 1.0) / (_cachedMaxScale - 1.0)).clamp(0.0, 1.0);
  }

  void _updateTransform(Offset position, double scale) {
    if (_isAnimating || !mounted) return;

    // Update ValueNotifiers for efficient rebuilds
    if (_positionNotifier.value != position) {
      _positionNotifier.value = position;
    }
    if (_scaleNotifier.value != scale) {
      _scaleNotifier.value = scale;
    }
  }

  Future<void> _animateToOriginal() async {
    if (_isAnimating || !mounted) return;

    _isAnimating = true;

    try {
      final currentScale = _scaleNotifier.value;
      final currentPosition = _positionNotifier.value;

      // Fix: Always animate back to original, even if scale is close to 1.0
      _scaleAnimation = Tween<double>(begin: currentScale, end: 1.0).animate(
        CurvedAnimation(
          parent: _animationController,
          curve: Curves.fastOutSlowIn,
        ),
      );

      _positionAnimation =
          Tween<Offset>(
            begin: currentPosition,
            end: widget.originalPosition,
          ).animate(
            CurvedAnimation(
              parent: _animationController,
              curve: Curves.fastOutSlowIn,
            ),
          );

      _opacityAnimation =
          Tween<double>(begin: _getOpacity(currentScale), end: 0.0).animate(
            CurvedAnimation(
              parent: _animationController,
              curve: Curves.fastOutSlowIn,
            ),
          );

      // Fix: Ensure animation runs even for small changes
      await _animationController.forward(from: 0.0);

      if (mounted) {
        _animationController.reset();
      }
    } catch (e) {
      debugPrint('Error in animation: $e');
    } finally {
      _isAnimating = false;
    }
  }
}
